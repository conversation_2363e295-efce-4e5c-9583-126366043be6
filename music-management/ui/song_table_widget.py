import os
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QPixmap, QIcon
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLabel, QHeaderView)
from .utils import get_song_cover


class CoverLoadThread(QThread):
    """封面加载线程"""
    cover_loaded = pyqtSignal(int, QPixmap)
    
    def __init__(self, row, file_path):
        super().__init__()
        self.row = row
        self.file_path = file_path
    
    def run(self):
        cover = get_song_cover(self.file_path)
        if cover:
            self.cover_loaded.emit(self.row, cover)


class SongTableWidget(QWidget):
    """歌曲表格组件"""
    
    def __init__(self, music_manager, config):
        super().__init__()
        self.music_manager = music_manager
        self.config = config
        self.current_page = 0
        self.total_pages = 0
        self.songs_per_page = config.get('entry_per_page', 100)
        self.cover_threads = []
        
        self.init_ui()
        self.load_songs()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(['封面', '歌手', '歌曲', '导入时间', '文件大小'])
        
        # 设置表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.verticalHeader().setVisible(False)
        
        # 设置列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 封面列固定宽度
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 歌手列自适应
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # 歌曲列自适应
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 时间列适应内容
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 大小列适应内容
        
        self.table.setColumnWidth(0, 80)  # 封面列宽度
        
        layout.addWidget(self.table)
        
        # 分页控件
        page_layout = QHBoxLayout()
        
        self.prev_button = QPushButton('上一页')
        self.prev_button.clicked.connect(self.prev_page)
        page_layout.addWidget(self.prev_button)
        
        self.page_label = QLabel('第 1 页，共 1 页')
        page_layout.addWidget(self.page_label)
        
        self.next_button = QPushButton('下一页')
        self.next_button.clicked.connect(self.next_page)
        page_layout.addWidget(self.next_button)
        
        page_layout.addStretch()
        
        # 刷新按钮
        self.refresh_button = QPushButton('刷新')
        self.refresh_button.clicked.connect(self.refresh)
        page_layout.addWidget(self.refresh_button)
        
        layout.addLayout(page_layout)
        self.setLayout(layout)
    
    def load_songs(self):
        """加载歌曲数据"""
        try:
            # 获取总数
            total_count = self.music_manager.DB.get_songs_count()
            self.total_pages = (total_count + self.songs_per_page - 1) // self.songs_per_page
            
            # 获取当前页数据
            offset = self.current_page * self.songs_per_page
            songs = self.music_manager.DB.get_all_songs(self.songs_per_page, offset)
            
            # 清空表格
            self.table.setRowCount(0)
            
            # 停止所有封面加载线程
            for thread in self.cover_threads:
                thread.quit()
                thread.wait()
            self.cover_threads.clear()
            
            # 填充表格
            for i, song in enumerate(songs):
                self.table.insertRow(i)
                
                # 封面列（先设置空的，后续异步加载）
                cover_item = QTableWidgetItem()
                cover_item.setFlags(cover_item.flags() & ~Qt.ItemIsEditable)
                self.table.setItem(i, 0, cover_item)
                
                # 歌手列
                singer_item = QTableWidgetItem(song.singer)
                singer_item.setFlags(singer_item.flags() & ~Qt.ItemIsEditable)
                self.table.setItem(i, 1, singer_item)
                
                # 歌曲列
                song_item = QTableWidgetItem(song.song)
                song_item.setFlags(song_item.flags() & ~Qt.ItemIsEditable)
                self.table.setItem(i, 2, song_item)
                
                # 导入时间列
                time_item = QTableWidgetItem(song.import_time)
                time_item.setFlags(time_item.flags() & ~Qt.ItemIsEditable)
                self.table.setItem(i, 3, time_item)
                
                # 文件大小列
                file_path = self.music_manager.MusicLibrarian.get_song_file_path(
                    f"{song.singer} - {song.song}.mp3"
                )
                size_text = self.get_file_size_text(file_path)
                size_item = QTableWidgetItem(size_text)
                size_item.setFlags(size_item.flags() & ~Qt.ItemIsEditable)
                self.table.setItem(i, 4, size_item)
                
                # 异步加载封面
                if os.path.exists(file_path):
                    thread = CoverLoadThread(i, file_path)
                    thread.cover_loaded.connect(self.on_cover_loaded)
                    thread.start()
                    self.cover_threads.append(thread)
            
            # 设置行高
            for i in range(self.table.rowCount()):
                self.table.setRowHeight(i, 80)
            
            self.update_page_controls()
            
        except Exception as e:
            print(f"加载歌曲数据时出错: {e}")
    
    def on_cover_loaded(self, row, pixmap):
        """封面加载完成回调"""
        if row < self.table.rowCount():
            item = self.table.item(row, 0)
            if item:
                item.setIcon(QIcon(pixmap))
    
    def get_file_size_text(self, file_path):
        """获取文件大小文本"""
        try:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                if size < 1024:
                    return f"{size} B"
                elif size < 1024 * 1024:
                    return f"{size / 1024:.1f} KB"
                else:
                    return f"{size / (1024 * 1024):.1f} MB"
            return "未知"
        except:
            return "未知"
    
    def update_page_controls(self):
        """更新分页控件状态"""
        self.page_label.setText(f'第 {self.current_page + 1} 页，共 {max(1, self.total_pages)} 页')
        self.prev_button.setEnabled(self.current_page > 0)
        self.next_button.setEnabled(self.current_page < self.total_pages - 1)
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 0:
            self.current_page -= 1
            self.load_songs()
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            self.load_songs()
    
    def refresh(self):
        """刷新数据"""
        self.load_songs()
    
    def update_config(self, config):
        """更新配置"""
        self.config = config
        old_per_page = self.songs_per_page
        self.songs_per_page = config.get('entry_per_page', 100)
        
        # 如果每页条目数改变，重新计算当前页
        if old_per_page != self.songs_per_page:
            current_item = self.current_page * old_per_page
            self.current_page = current_item // self.songs_per_page
            self.load_songs()
