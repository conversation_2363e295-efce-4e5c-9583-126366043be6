from PyQt5.QtCore import pyqtSignal
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                             QSpinBox, QLineEdit, QGroupBox, QLabel, QPushButton)
from .utils import save_config, show_message


class ConfigWidget(QWidget):
    """配置页面组件"""
    
    config_changed = pyqtSignal(dict)  # 配置改变信号
    
    def __init__(self, config):
        super().__init__()
        self.config = config.copy()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 显示设置组
        display_group = QGroupBox("显示设置")
        display_layout = QFormLayout()
        
        # 每页条目数设置
        self.entry_per_page_spinbox = QSpinBox()
        self.entry_per_page_spinbox.setRange(10, 1000)
        self.entry_per_page_spinbox.setValue(self.config.get('entry_per_page', 100))
        self.entry_per_page_spinbox.valueChanged.connect(self.on_config_changed)
        display_layout.addRow("每页显示条目数:", self.entry_per_page_spinbox)
        
        display_group.setLayout(display_layout)
        layout.addWidget(display_group)
        
        # 路径设置组
        path_group = QGroupBox("路径设置")
        path_layout = QFormLayout()
        
        # 数据库路径
        self.db_path_edit = QLineEdit()
        self.db_path_edit.setText(self.config.get('db_path', 'music_manager.db'))
        self.db_path_edit.textChanged.connect(self.on_config_changed)
        path_layout.addRow("数据库文件:", self.db_path_edit)
        
        # 音乐库路径
        self.music_folder_edit = QLineEdit()
        self.music_folder_edit.setText(self.config.get('music_folder', 'music_library'))
        self.music_folder_edit.textChanged.connect(self.on_config_changed)
        path_layout.addRow("音乐库文件夹:", self.music_folder_edit)
        
        # 解码器路径
        self.decoder_folder_edit = QLineEdit()
        self.decoder_folder_edit.setText(self.config.get('decoder_folder', 'resources'))
        self.decoder_folder_edit.textChanged.connect(self.on_config_changed)
        path_layout.addRow("解码器文件夹:", self.decoder_folder_edit)
        
        path_group.setLayout(path_layout)
        layout.addWidget(path_group)
        
        # 说明文本
        info_label = QLabel("""
<b>配置说明:</b><br>
• <b>每页显示条目数</b>: 控制歌曲列表和日志列表每页显示的条目数量<br>
• <b>数据库文件</b>: SQLite数据库文件的路径<br>
• <b>音乐库文件夹</b>: 存储音乐文件的文件夹路径<br>
• <b>解码器文件夹</b>: 存储解码工具的文件夹路径<br><br>
<i>注意: 修改路径设置后需要重启程序才能生效</i>
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("QLabel { color: #6b5b73; padding: 10px; }")
        layout.addWidget(info_label)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.reset_button = QPushButton("重置为默认值")
        self.reset_button.clicked.connect(self.reset_to_default)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        self.save_button = QPushButton("保存配置")
        self.save_button.clicked.connect(self.save_config)
        button_layout.addWidget(self.save_button)
        
        layout.addLayout(button_layout)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def on_config_changed(self):
        """配置改变时的处理"""
        # 更新内部配置
        self.config['entry_per_page'] = self.entry_per_page_spinbox.value()
        self.config['db_path'] = self.db_path_edit.text().strip()
        self.config['music_folder'] = self.music_folder_edit.text().strip()
        self.config['decoder_folder'] = self.decoder_folder_edit.text().strip()
        
        # 发送配置改变信号（仅对显示相关的配置）
        display_config = {
            'entry_per_page': self.config['entry_per_page']
        }
        self.config_changed.emit(display_config)
    
    def reset_to_default(self):
        """重置为默认值"""
        default_config = {
            "entry_per_page": 100,
            "db_path": "music_manager.db",
            "music_folder": "music_library",
            "decoder_folder": "resources"
        }
        
        self.entry_per_page_spinbox.setValue(default_config['entry_per_page'])
        self.db_path_edit.setText(default_config['db_path'])
        self.music_folder_edit.setText(default_config['music_folder'])
        self.decoder_folder_edit.setText(default_config['decoder_folder'])
        
        show_message(self, "重置完成", "配置已重置为默认值")
    
    def save_config(self):
        """保存配置"""
        try:
            save_config(self.config)
            show_message(self, "保存成功", "配置已保存")
        except Exception as e:
            show_message(self, "保存失败", f"保存配置时出错: {e}", "error")
    
    def update_config(self, config):
        """更新配置"""
        self.config = config.copy()
        
        # 更新UI控件
        self.entry_per_page_spinbox.setValue(config.get('entry_per_page', 100))
        self.db_path_edit.setText(config.get('db_path', 'music_manager.db'))
        self.music_folder_edit.setText(config.get('music_folder', 'music_library'))
        self.decoder_folder_edit.setText(config.get('decoder_folder', 'resources'))
