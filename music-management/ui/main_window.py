import os
import sys
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QTabWidget, QPushButton, QLineEdit, QLabel, 
                             QFileDialog, QProgressBar, QMessageBox)
from PyQt5.QtGui import QIcon

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from MusicManager import MusicManager
from .song_table_widget import SongTableWidget
from .log_table_widget import LogTableWidget
from .config_widget import ConfigWidget
from .utils import load_config, get_app_icon, apply_morandi_style, show_message


class OperationThread(QThread):
    """操作线程"""
    progress_updated = pyqtSignal(int)
    operation_finished = pyqtSignal(bool, str)

    def __init__(self, config, operation, folder_path=None):
        super().__init__()
        self.config = config
        self.operation = operation
        self.folder_path = folder_path

    def run(self):
        try:
            # 在线程中创建新的MusicManager实例以避免SQLite线程问题
            music_manager = MusicManager(
                db_path=self.config.get('db_path', 'music_manager.db'),
                music_folder=self.config.get('music_folder', 'music_library'),
                decoder_folder=self.config.get('decoder_folder', 'resources')
            )

            if self.operation == 'import':
                result = music_manager.import_music(self.folder_path)
                self.operation_finished.emit(True, f"成功导入 {len(result)} 首歌曲")
            elif self.operation == 'export':
                music_manager.export_music(self.folder_path)
                self.operation_finished.emit(True, f"成功导出到 {self.folder_path}")
            elif self.operation == 'decode':
                music_manager.decode_files(self.folder_path)
                self.operation_finished.emit(True, f"成功解码 {self.folder_path} 中的文件")
            elif self.operation == 'compare':
                music_manager.compare_library(self.folder_path)
                self.operation_finished.emit(True, f"成功比较并清理 {self.folder_path}")

            # 关闭线程中的MusicManager
            music_manager.close()

        except Exception as e:
            self.operation_finished.emit(False, str(e))


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.config = load_config()
        self.music_manager = None
        self.operation_thread = None
        
        self.init_music_manager()
        self.init_ui()
        self.apply_style()
    
    def init_music_manager(self):
        """初始化音乐管理器"""
        try:
            self.music_manager = MusicManager(
                db_path=self.config.get('db_path', 'music_manager.db'),
                music_folder=self.config.get('music_folder', 'music_library'),
                decoder_folder=self.config.get('decoder_folder', 'resources')
            )
        except Exception as e:
            QMessageBox.critical(None, "初始化错误", f"初始化音乐管理器失败: {e}")
            sys.exit(1)
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("音乐管理系统")
        self.setWindowIcon(get_app_icon())
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建歌曲页面
        self.create_songs_page()
        
        # 创建日志页面
        self.create_logs_page()
        
        # 创建配置页面
        self.create_config_page()
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def create_songs_page(self):
        """创建歌曲页面"""
        songs_widget = QWidget()
        layout = QVBoxLayout()
        
        # 文件夹选择区域
        folder_layout = QHBoxLayout()
        
        folder_layout.addWidget(QLabel("选择文件夹:"))
        
        self.folder_edit = QLineEdit()
        self.folder_edit.setPlaceholderText("请选择要操作的文件夹...")
        folder_layout.addWidget(self.folder_edit)
        
        self.browse_button = QPushButton("浏览")
        self.browse_button.clicked.connect(self.browse_folder)
        folder_layout.addWidget(self.browse_button)
        
        layout.addLayout(folder_layout)
        
        # 功能按钮区域
        button_layout = QHBoxLayout()
        
        self.import_button = QPushButton("导入音乐")
        self.import_button.clicked.connect(lambda: self.execute_operation('import'))
        button_layout.addWidget(self.import_button)
        
        self.export_button = QPushButton("导出音乐")
        self.export_button.clicked.connect(lambda: self.execute_operation('export'))
        button_layout.addWidget(self.export_button)
        
        self.decode_button = QPushButton("解码文件")
        self.decode_button.clicked.connect(lambda: self.execute_operation('decode'))
        button_layout.addWidget(self.decode_button)
        
        self.compare_button = QPushButton("比较清理")
        self.compare_button.clicked.connect(lambda: self.execute_operation('compare'))
        button_layout.addWidget(self.compare_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 歌曲表格
        self.song_table = SongTableWidget(self.music_manager, self.config)
        layout.addWidget(self.song_table)
        
        songs_widget.setLayout(layout)
        self.tab_widget.addTab(songs_widget, "歌曲库")
    
    def create_logs_page(self):
        """创建日志页面"""
        self.log_table = LogTableWidget(self.music_manager, self.config)
        self.tab_widget.addTab(self.log_table, "变更历史")
    
    def create_config_page(self):
        """创建配置页面"""
        self.config_widget = ConfigWidget(self.config)
        self.config_widget.config_changed.connect(self.on_config_changed)
        self.tab_widget.addTab(self.config_widget, "配置")
    
    def apply_style(self):
        """应用样式"""
        self.setStyleSheet(apply_morandi_style())
    
    def browse_folder(self):
        """浏览文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择文件夹")
        if folder:
            self.folder_edit.setText(folder)
    
    def execute_operation(self, operation):
        """执行操作"""
        folder_path = self.folder_edit.text().strip()
        if not folder_path:
            show_message(self, "错误", "请先选择文件夹", "error")
            return
        
        if not os.path.exists(folder_path):
            show_message(self, "错误", "选择的文件夹不存在", "error")
            return
        
        # 确认操作
        operation_names = {
            'import': '导入音乐',
            'export': '导出音乐', 
            'decode': '解码文件',
            'compare': '比较清理'
        }
        
        if not show_message(self, "确认操作", 
                           f"确定要执行 {operation_names[operation]} 操作吗？\n文件夹: {folder_path}", 
                           "question"):
            return
        
        # 禁用按钮
        self.set_buttons_enabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        
        # 启动操作线程
        self.operation_thread = OperationThread(self.config, operation, folder_path)
        self.operation_thread.operation_finished.connect(self.on_operation_finished)
        self.operation_thread.start()
        
        self.statusBar().showMessage(f"正在执行 {operation_names[operation]}...")
    
    def set_buttons_enabled(self, enabled):
        """设置按钮启用状态"""
        self.import_button.setEnabled(enabled)
        self.export_button.setEnabled(enabled)
        self.decode_button.setEnabled(enabled)
        self.compare_button.setEnabled(enabled)
        self.browse_button.setEnabled(enabled)
    
    def on_operation_finished(self, success, message):
        """操作完成回调"""
        self.set_buttons_enabled(True)
        self.progress_bar.setVisible(False)
        
        if success:
            show_message(self, "操作成功", message)
            # 刷新数据
            self.song_table.refresh()
            self.log_table.refresh()
            self.statusBar().showMessage("操作完成")
        else:
            show_message(self, "操作失败", f"操作失败: {message}", "error")
            self.statusBar().showMessage("操作失败")
    
    def on_config_changed(self, config):
        """配置改变回调"""
        # 更新配置
        self.config.update(config)
        
        # 更新组件配置
        self.song_table.update_config(self.config)
        self.log_table.update_config(self.config)
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.operation_thread and self.operation_thread.isRunning():
            reply = show_message(self, "确认退出", "有操作正在进行中，确定要退出吗？", "question")
            if not reply:
                event.ignore()
                return
            
            self.operation_thread.quit()
            self.operation_thread.wait()
        
        if self.music_manager:
            self.music_manager.close()
        
        event.accept()
