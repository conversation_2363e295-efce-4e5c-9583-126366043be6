# MusicManagement
A tool for managing my own music data.

Usage
```bash
usage: MusicManager.py [-h] -d DIRECTORY {decode,import,export,compare,help}
```

## Commands
- decode: Decode music file in a directory.
- import: Import music data from a directory.
- export: Export music data to a directory at Full Export or Incremental Export.
- compare: Compare the music data in the directory with the database, and remove the same music file in the directory.
- help: Show help message and exit.