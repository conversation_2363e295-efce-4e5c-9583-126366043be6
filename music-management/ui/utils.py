import json
import os
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap, QIcon
from PyQt5.QtWidgets import QApplication
try:
    from mutagen.mp3 import MP3
    from mutagen.id3 import ID3, APIC
    MUTAGEN_AVAILABLE = True
except ImportError:
    MUTAGEN_AVAILABLE = False


def load_config():
    """加载配置文件"""
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        # 默认配置
        return {
            "entry_per_page": 100,
            "db_path": "music_manager.db",
            "music_folder": "music_library",
            "decoder_folder": "resources"
        }


def save_config(config):
    """保存配置文件"""
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=4, ensure_ascii=False)


def get_app_icon():
    """获取应用图标"""
    # 获取项目根目录的绝对路径
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    icon_path = os.path.join(project_root, 'resources', 'logo.png')

    if os.path.exists(icon_path):
        return QIcon(icon_path)

    return QIcon()


def get_song_cover(song_file_path):
    """获取歌曲封面"""
    if not MUTAGEN_AVAILABLE:
        return None

    try:
        if not os.path.exists(song_file_path):
            return None

        audio = MP3(song_file_path, ID3=ID3)

        # 查找封面
        for tag in audio.tags.values():
            if isinstance(tag, APIC):
                pixmap = QPixmap()
                pixmap.loadFromData(tag.data)
                return pixmap.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation)

        return None
    except Exception:
        return None


def apply_morandi_style():
    """应用Morandi色彩风格样式表"""
    return """
    QMainWindow {
        background-color: #f5f3f0;
        color: #5a5a5a;
    }
    
    QTabWidget::pane {
        border: 1px solid #d4c4b0;
        background-color: #f8f6f3;
    }
    
    QTabWidget::tab-bar {
        alignment: center;
    }
    
    QTabBar::tab {
        background-color: #e8ddd4;
        color: #6b5b73;
        padding: 6px 12px;
        margin-right: 2px;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        font-size: 12px;
    }
    
    QTabBar::tab:selected {
        background-color: #d4a5a5;
        color: #ffffff;
    }
    
    QTabBar::tab:hover {
        background-color: #dcc7c7;
    }
    
    QTableWidget {
        background-color: #ffffff;
        alternate-background-color: #f9f7f4;
        gridline-color: #e0d6cc;
        selection-background-color: #c8a8a8;
        selection-color: #ffffff;
        border: 1px solid #d4c4b0;
    }
    
    QTableWidget::item {
        padding: 4px;
        border-bottom: 1px solid #f0ebe4;
    }
    
    QHeaderView::section {
        background-color: #e8ddd4;
        color: #6b5b73;
        padding: 6px;
        border: none;
        border-right: 1px solid #d4c4b0;
        font-weight: bold;
        font-size: 12px;
    }
    
    QPushButton {
        background-color: #d4a5a5;
        color: #ffffff;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-weight: bold;
        font-size: 12px;
    }
    
    QPushButton:hover {
        background-color: #c8a8a8;
    }
    
    QPushButton:pressed {
        background-color: #b89898;
    }
    
    QPushButton:disabled {
        background-color: #e8ddd4;
        color: #a0a0a0;
    }
    
    QLineEdit {
        background-color: #ffffff;
        border: 2px solid #e8ddd4;
        border-radius: 4px;
        padding: 8px;
        color: #5a5a5a;
    }
    
    QLineEdit:focus {
        border-color: #d4a5a5;
    }
    
    QSpinBox {
        background-color: #ffffff;
        border: 2px solid #e8ddd4;
        border-radius: 4px;
        padding: 8px;
        color: #5a5a5a;
    }
    
    QSpinBox:focus {
        border-color: #d4a5a5;
    }
    
    QLabel {
        color: #6b5b73;
    }
    
    QGroupBox {
        font-weight: bold;
        color: #6b5b73;
        border: 2px solid #e8ddd4;
        border-radius: 6px;
        margin-top: 10px;
        padding-top: 10px;
    }
    
    QGroupBox::title {
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 5px 0 5px;
    }
    
    QProgressBar {
        border: 2px solid #e8ddd4;
        border-radius: 4px;
        text-align: center;
        background-color: #f8f6f3;
    }
    
    QProgressBar::chunk {
        background-color: #d4a5a5;
        border-radius: 2px;
    }
    """


def show_message(parent, title, message, message_type="info"):
    """显示消息对话框"""
    from PyQt5.QtWidgets import QMessageBox

    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)

    # 移除所有图标，只显示文字
    msg_box.setIcon(QMessageBox.NoIcon)

    if message_type == "question":
        msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        return msg_box.exec_() == QMessageBox.Yes
    else:
        msg_box.setStandardButtons(QMessageBox.Ok)

    msg_box.exec_()
    return True
